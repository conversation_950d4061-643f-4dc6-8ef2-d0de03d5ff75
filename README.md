# 🐸 PepeAuth - Modern License Management Platform

A modern, secure license management platform built with Flask, inspired by KeyAuth but with a fresh Pepe-themed design. PepeAuth provides developers with a simple yet powerful way to manage software licenses, validate keys, and track usage analytics.

## ✨ Features

- **🔐 Secure Authentication** - Bcrypt password hashing, session management
- **📱 Modern UI** - Dark theme with glassmorphism effects and Pepe-green accents
- **🚀 Fast API** - RESTful API for license validation with rate limiting
- **📊 Analytics** - Real-time usage statistics and key tracking
- **👥 Multi-tier Plans** - Free, Pro, and Military plans with different limits
- **🔧 Admin Panel** - Comprehensive admin interface for user and system management
- **🔑 HWID Binding** - Optional hardware ID binding for enhanced security
- **⚡ Rate Limiting** - Configurable API rate limits per plan
- **🧪 Fully Tested** - Comprehensive test suite with pytest

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- pip (Python package manager)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/pepeauth.git
   cd pepeauth
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python run.py
   ```

4. **Access the application**
   - Open your browser and go to `http://127.0.0.1:5000`
   - Register a new account or use demo credentials

### Demo Credentials

- **Admin User**: `<EMAIL>` / `admin123`
- **Regular User**: `<EMAIL>` / `demo123`

## 📁 Project Structure

```
pepeauth/
├── app/
│   ├── __init__.py          # Flask app factory and configuration
│   ├── schema.sql           # Database schema with default data
│   ├── auth.py              # Authentication routes and User model
│   ├── dashboard.py         # User dashboard and app management
│   ├── admin.py             # Admin panel functionality
│   ├── api.py               # API endpoints for license validation
│   ├── forms.py             # Flask-WTF forms and validation
│   ├── templates/           # Jinja2 HTML templates
│   │   ├── base.html        # Base template with navigation
│   │   ├── index.html       # Landing page
│   │   ├── login.html       # Login page
│   │   ├── register.html    # Registration page
│   │   ├── dashboard.html   # User dashboard
│   │   └── app_detail.html  # App management page
│   └── static/
│       └── css/
│           └── pepe.css     # Custom glassmorphism styles
├── tests/                   # Comprehensive test suite
│   ├── conftest.py          # Test configuration and fixtures
│   ├── test_auth.py         # Authentication tests
│   ├── test_api.py          # API endpoint tests
│   ├── test_dashboard.py    # Dashboard functionality tests
│   └── test_admin.py        # Admin panel tests
├── database.db              # SQLite database (auto-created)
├── run.py                   # Application entry point
├── requirements.txt         # Python dependencies
├── pytest.ini              # Test configuration
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables

- `FLASK_HOST` - Host to bind to (default: 127.0.0.1)
- `FLASK_PORT` - Port to bind to (default: 5000)
- `FLASK_DEBUG` - Enable debug mode (default: False)
- `SECRET_KEY` - Flask secret key for sessions (auto-generated in dev)

### Database

PepeAuth uses SQLite by default with the following tables:

- **plan** - Subscription plans (Free, Pro, Military)
- **user** - User accounts with plan assignments
- **app** - User applications with settings
- **license_key** - License keys with usage tracking

## 🔌 API Documentation

### Authentication Endpoint

**POST** `/api/v1/auth`

Authenticate and activate a license key.

**Request Body:**
```json
{
  "app_name": "MyApp",
  "key": "550e8400-e29b-41d4-a716-************",
  "hwid": "unique-hardware-id" // Optional
}
```

**Response (Success):**
```json
{
  "status": true,
  "message": "License activated successfully",
  "expires": "2025-02-23T10:30:00"
}
```

**Response (Error):**
```json
{
  "status": false,
  "message": "Invalid license key"
}
```

### Key Validation Endpoint

**POST** `/api/v1/check_key`

Check license key status without activating it.

**Request Body:**
```json
{
  "app_name": "MyApp",
  "key": "550e8400-e29b-41d4-a716-************"
}
```

**Response:**
```json
{
  "status": true,
  "key_info": {
    "used": false,
    "created_at": "2025-01-24T10:00:00",
    "used_at": null,
    "expires_at": null,
    "expired": false,
    "hwid": null
  }
}
```

### App Information Endpoint

**POST** `/api/v1/app_info`

Get application information and statistics.

**Request Body:**
```json
{
  "app_name": "MyApp",
  "secret": "app-secret-key" // Optional, required for statistics
}
```

### Rate Limits

- **Free Plan**: 100 requests/day
- **Pro Plan**: 1,000 requests/day  
- **Military Plan**: Unlimited

## 💳 Plans & Pricing

| Feature | Free | Pro | Military |
|---------|------|-----|----------|
| Apps | 1 | 3 | Unlimited |
| License Keys | 50 | 1,000 | Unlimited |
| API Calls/day | 100 | 1,000 | Unlimited |
| HWID Binding | ✅ | ✅ | ✅ |
| Analytics | Basic | Advanced | Custom |
| Support | Community | Priority | 24/7 Dedicated |

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_api.py

# Run with verbose output
pytest -v

# Run only fast tests
pytest -m "not slow"
```

### Test Coverage

- **Authentication**: Registration, login, logout, user model
- **Dashboard**: App creation, key generation, management
- **API**: License validation, key checking, rate limiting
- **Admin**: User management, plan management, system stats

## 🔒 Security Features

- **Password Hashing**: Bcrypt with salt
- **CSRF Protection**: Flask-WTF CSRF tokens
- **Session Security**: Secure session cookies
- **Rate Limiting**: Configurable API rate limits
- **HWID Binding**: Optional hardware ID validation
- **Input Validation**: Comprehensive form validation
- **SQL Injection Protection**: Parameterized queries

## 🎨 Theming

PepeAuth features a modern dark theme with:

- **Colors**: Dark backgrounds (#111827) with Pepe-green accents (#2ecc71)
- **Effects**: Glassmorphism cards with backdrop blur
- **Typography**: Inter font family for clean readability
- **Responsive**: Mobile-first responsive design
- **Animations**: Smooth transitions and hover effects

## 🚀 Deployment

### Development

```bash
export FLASK_DEBUG=true
python run.py
```

### Production

1. **Set environment variables**
   ```bash
   export FLASK_DEBUG=false
   export SECRET_KEY="your-secret-key-here"
   ```

2. **Use a production WSGI server**
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 "app:create_app()"
   ```

3. **Set up reverse proxy** (nginx, Apache, etc.)

4. **Configure database** (PostgreSQL, MySQL for production)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by KeyAuth license management system
- Built with Flask and modern web technologies
- Pepe theme for the memes 🐸

## 📞 Support

- **Documentation**: This README and inline code comments
- **Issues**: GitHub Issues for bug reports and feature requests
- **Community**: Join our Discord server (link coming soon)

---

**Made with 🐸 and Flask** | © 2025 PepeAuth
