#!/usr/bin/env python3
"""
Create demo data for PepeAuth testing
"""

import sqlite3
import uuid
from datetime import datetime
from app import create_app, bcrypt

def create_demo_data():
    """Create demo users and data for testing"""
    print("🐸 Creating demo data for PepeAuth...")
    
    app = create_app()
    with app.app_context():
        conn = sqlite3.connect(app.config['DATABASE'])
        cursor = conn.cursor()
        
        # Create admin user
        admin_password = bcrypt.generate_password_hash('admin123').decode('utf-8')
        cursor.execute('''
            INSERT OR IGNORE INTO user (email, username, password, plan_id, is_admin) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('<EMAIL>', 'admin', admin_password, 3, 1))
        
        # Create demo user
        demo_password = bcrypt.generate_password_hash('demo123').decode('utf-8')
        cursor.execute('''
            INSERT OR IGNORE INTO user (email, username, password, plan_id, is_admin) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('<EMAIL>', 'demo', demo_password, 2, 0))
        
        # Get demo user ID
        cursor.execute('SELECT id FROM user WHERE username = ?', ('demo',))
        demo_user = cursor.fetchone()
        
        if demo_user:
            demo_user_id = demo_user[0]
            
            # Create demo app
            app_secret = 'demo-app-secret-' + str(uuid.uuid4())[:8]
            cursor.execute('''
                INSERT OR IGNORE INTO app (name, owner_id, secret, hwid_lock, expiry_days) 
                VALUES (?, ?, ?, ?, ?)
            ''', ('DemoApp', demo_user_id, app_secret, 0, 30))
            
            # Get app ID
            cursor.execute('SELECT id FROM app WHERE name = ?', ('DemoApp',))
            demo_app = cursor.fetchone()
            
            if demo_app:
                demo_app_id = demo_app[0]
                
                # Create some demo license keys
                for i in range(5):
                    license_key = str(uuid.uuid4())
                    cursor.execute('''
                        INSERT OR IGNORE INTO license_key (key, app_id) 
                        VALUES (?, ?)
                    ''', (license_key, demo_app_id))
                
                print(f"✅ Created demo app 'DemoApp' with 5 license keys")
                print(f"   App Secret: {app_secret}")
        
        conn.commit()
        conn.close()
        
        print("✅ Demo data created successfully!")
        print("\nDemo Credentials:")
        print("  Admin: <EMAIL> / admin123")
        print("  User:  <EMAIL> / demo123")
        print("\nYou can now test the full application!")

if __name__ == '__main__':
    create_demo_data()
