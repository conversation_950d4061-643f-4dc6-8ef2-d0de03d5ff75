#!/usr/bin/env python3
"""
Test user registration functionality
"""

from app import create_app

def test_registration():
    """Test user registration"""
    print("🔍 Testing user registration...")
    
    app = create_app()
    
    with app.test_client() as client:
        # Disable CSRF for testing
        app.config['WTF_CSRF_ENABLED'] = False
        
        try:
            # Test registration
            response = client.post('/auth/register', data={
                'email': '<EMAIL>',
                'username': 'newtest',
                'password': 'testpass123',
                'confirm_password': 'testpass123'
            })
            
            print(f"Registration response status: {response.status_code}")
            
            if response.status_code == 302:
                print("✅ Registration successful (redirected)")
                
                # Check if user was created
                with app.app_context():
                    from app import query_db
                    user = query_db('SELECT * FROM user WHERE email = ?', ('<EMAIL>',), one=True)
                    if user:
                        print(f"✅ User created: {user['username']} ({user['email']})")
                    else:
                        print("❌ User not found in database")
            else:
                print(f"❌ Registration failed with status {response.status_code}")
                print("Response data:", response.get_data(as_text=True)[:200])
                
        except Exception as e:
            print(f"❌ Registration test error: {e}")
            import traceback
            traceback.print_exc()

def test_login():
    """Test user login with demo account"""
    print("\n🔍 Testing user login...")
    
    app = create_app()
    
    with app.test_client() as client:
        # Disable CSRF for testing
        app.config['WTF_CSRF_ENABLED'] = False
        
        try:
            # Test login with demo account
            response = client.post('/auth/login', data={
                'username': 'demo',
                'password': 'demo123'
            })
            
            print(f"Login response status: {response.status_code}")
            
            if response.status_code == 302:
                print("✅ Login successful (redirected)")
                
                # Test accessing dashboard after login
                response = client.get('/dashboard/')
                if response.status_code == 200:
                    print("✅ Dashboard accessible after login")
                else:
                    print(f"❌ Dashboard not accessible: {response.status_code}")
            else:
                print(f"❌ Login failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ Login test error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_registration()
    test_login()
