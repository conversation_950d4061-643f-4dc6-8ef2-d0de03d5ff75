-- PepeAuth Database Schema
-- KeyAuth-style license management system

-- Plans table
CREATE TABLE IF NOT EXISTS plan (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  max_apps INTEGER NOT NULL,
  max_keys INTEGER NOT NULL,
  rate_limit INTEGER NOT NULL DEFAULT 100
);

-- Insert default plans
INSERT OR IGNORE INTO plan (name, max_apps, max_keys, rate_limit) VALUES
  ('Free', 1, 50, 100),
  ('Pro', 3, 1000, 1000),
  ('Military', -1, -1, -1);

-- Users table
CREATE TABLE IF NOT EXISTS user (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  plan_id INTEGER NOT NULL DEFAULT 1,
  is_admin INTEGER NOT NULL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIG<PERSON> KEY (plan_id) REFERENCES plan(id)
);

-- Apps table
CREATE TABLE IF NOT EXISTS app (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  owner_id INTEGER NOT NULL,
  secret TEXT UNIQUE NOT NULL,
  hwid_lock INTEGER NOT NULL DEFAULT 0,
  expiry_days INTEGER NOT NULL DEFAULT 30,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES user(id) ON DELETE CASCADE
);

-- License keys table
CREATE TABLE IF NOT EXISTS license_key (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  app_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  used INTEGER NOT NULL DEFAULT 0,
  used_at DATETIME,
  hwid TEXT,
  expires_at DATETIME,
  FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_email ON user(email);
CREATE INDEX IF NOT EXISTS idx_user_username ON user(username);
CREATE INDEX IF NOT EXISTS idx_app_owner ON app(owner_id);
CREATE INDEX IF NOT EXISTS idx_app_secret ON app(secret);
CREATE INDEX IF NOT EXISTS idx_license_key_key ON license_key(key);
CREATE INDEX IF NOT EXISTS idx_license_key_app ON license_key(app_id);
