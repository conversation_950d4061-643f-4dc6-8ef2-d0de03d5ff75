#!/usr/bin/env python3
"""
Manual testing script for PepeAuth
Tests core functionality without requiring external dependencies
"""

import sqlite3
import os
import sys
from app import create_app

def test_database_setup():
    """Test database initialization"""
    print("🔍 Testing database setup...")
    
    app = create_app()
    with app.app_context():
        # Check if database file exists
        db_path = app.config['DATABASE']
        if os.path.exists(db_path):
            print("✅ Database file exists")
            
            # Check tables
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if all required tables exist
            tables = ['plan', 'user', 'app', 'license_key']
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            for table in tables:
                if table in existing_tables:
                    print(f"✅ Table '{table}' exists")
                else:
                    print(f"❌ Table '{table}' missing")
            
            # Check default plans
            cursor.execute("SELECT COUNT(*) FROM plan")
            plan_count = cursor.fetchone()[0]
            print(f"✅ Found {plan_count} plans in database")
            
            conn.close()
        else:
            print("❌ Database file not found")

def test_app_creation():
    """Test Flask app creation"""
    print("\n🔍 Testing Flask app creation...")
    
    try:
        app = create_app()
        print("✅ Flask app created successfully")
        
        # Test app configuration
        if app.config.get('SECRET_KEY'):
            print("✅ Secret key configured")
        else:
            print("❌ Secret key missing")
            
        # Test blueprints
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['auth', 'dashboard', 'admin', 'api']
        
        for bp_name in expected_blueprints:
            if bp_name in blueprint_names:
                print(f"✅ Blueprint '{bp_name}' registered")
            else:
                print(f"❌ Blueprint '{bp_name}' missing")
                
    except Exception as e:
        print(f"❌ Error creating Flask app: {e}")

def test_routes():
    """Test route registration"""
    print("\n🔍 Testing route registration...")
    
    try:
        app = create_app()
        
        # Test client
        with app.test_client() as client:
            # Test index route
            response = client.get('/')
            if response.status_code == 200:
                print("✅ Index route working")
            else:
                print(f"❌ Index route failed: {response.status_code}")
            
            # Test auth routes
            response = client.get('/auth/login')
            if response.status_code == 200:
                print("✅ Login route working")
            else:
                print(f"❌ Login route failed: {response.status_code}")
            
            response = client.get('/auth/register')
            if response.status_code == 200:
                print("✅ Register route working")
            else:
                print(f"❌ Register route failed: {response.status_code}")
            
            # Test protected routes (should redirect)
            response = client.get('/dashboard/')
            if response.status_code == 302:
                print("✅ Dashboard route protected (redirects)")
            else:
                print(f"❌ Dashboard route not protected: {response.status_code}")
            
            # Test API routes
            response = client.post('/api/v1/auth', json={})
            if response.status_code in [400, 404]:  # Expected error for empty request
                print("✅ API auth route working")
            else:
                print(f"❌ API auth route failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error testing routes: {e}")

def test_user_registration():
    """Test user registration functionality"""
    print("\n🔍 Testing user registration...")
    
    try:
        app = create_app()
        
        with app.test_client() as client:
            # Test user registration
            response = client.post('/auth/register', data={
                'email': '<EMAIL>',
                'username': 'testuser',
                'password': 'testpass123',
                'confirm_password': 'testpass123'
            })
            
            if response.status_code == 302:  # Redirect after successful registration
                print("✅ User registration working")
                
                # Check if user was created in database
                with app.app_context():
                    conn = sqlite3.connect(app.config['DATABASE'])
                    cursor = conn.cursor()
                    cursor.execute("SELECT * FROM user WHERE email = ?", ('<EMAIL>',))
                    user = cursor.fetchone()
                    
                    if user:
                        print("✅ User created in database")
                    else:
                        print("❌ User not found in database")
                    
                    conn.close()
            else:
                print(f"❌ User registration failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error testing user registration: {e}")

def main():
    """Run all tests"""
    print("🐸 PepeAuth Manual Testing")
    print("=" * 50)
    
    test_database_setup()
    test_app_creation()
    test_routes()
    test_user_registration()
    
    print("\n" + "=" * 50)
    print("🎉 Manual testing completed!")
    print("\nTo test the full application:")
    print("1. Run: python run.py")
    print("2. Open: http://127.0.0.1:5000")
    print("3. Register a new account or use demo credentials")
    print("4. Create an app and generate license keys")
    print("5. Test the API endpoints")

if __name__ == '__main__':
    main()
