from functools import wraps
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import query_db, execute_db
from app.forms import AdminUserForm, AdminPlanForm, SearchForm

bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('Admin access required.', 'error')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """Admin dashboard"""
    # Get system statistics
    stats = {
        'users': query_db('SELECT COUNT(*) as count FROM user', one=True)['count'],
        'apps': query_db('SELECT COUNT(*) as count FROM app', one=True)['count'],
        'keys': query_db('SELECT COUNT(*) as count FROM license_key', one=True)['count'],
        'used_keys': query_db('SELECT COUNT(*) as count FROM license_key WHERE used = 1', one=True)['count']
    }
    
    # Get recent activity
    recent_users = query_db(
        'SELECT username, email, created_at FROM user ORDER BY created_at DESC LIMIT 5'
    )
    
    recent_apps = query_db(
        'SELECT a.name, u.username, a.created_at FROM app a '
        'JOIN user u ON a.owner_id = u.id ORDER BY a.created_at DESC LIMIT 5'
    )
    
    # Get plan distribution
    plan_stats = query_db(
        'SELECT p.name, COUNT(u.id) as user_count FROM plan p '
        'LEFT JOIN user u ON p.id = u.plan_id GROUP BY p.id, p.name'
    )
    
    return render_template('admin.html', 
                         stats=stats, 
                         recent_users=recent_users,
                         recent_apps=recent_apps,
                         plan_stats=plan_stats)

@bp.route('/users')
@login_required
@admin_required
def users():
    """Manage users"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    search = request.args.get('search', '')
    
    # Build query
    base_query = '''
        SELECT u.*, p.name as plan_name 
        FROM user u 
        JOIN plan p ON u.plan_id = p.id
    '''
    
    if search:
        base_query += ' WHERE u.username LIKE ? OR u.email LIKE ?'
        search_param = f'%{search}%'
        users = query_db(base_query + ' ORDER BY u.created_at DESC', 
                        (search_param, search_param))
    else:
        users = query_db(base_query + ' ORDER BY u.created_at DESC')
    
    # Simple pagination
    start = (page - 1) * per_page
    end = start + per_page
    paginated_users = users[start:end]
    
    has_prev = page > 1
    has_next = len(users) > end
    
    return render_template('admin_users.html', 
                         users=paginated_users,
                         page=page,
                         has_prev=has_prev,
                         has_next=has_next,
                         search=search)

@bp.route('/user/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Edit user details"""
    user = query_db('SELECT * FROM user WHERE id = ?', (user_id,), one=True)
    if not user:
        flash('User not found.', 'error')
        return redirect(url_for('admin.users'))
    
    # Get available plans
    plans = query_db('SELECT * FROM plan ORDER BY name')
    
    form = AdminUserForm()
    form.plan_id.choices = [(p['id'], p['name']) for p in plans]
    
    if form.validate_on_submit():
        try:
            execute_db(
                'UPDATE user SET plan_id = ?, is_admin = ? WHERE id = ?',
                (form.plan_id.data, form.is_admin.data, user_id)
            )
            flash('User updated successfully.', 'success')
            return redirect(url_for('admin.users'))
        except Exception as e:
            flash('Failed to update user.', 'error')
    
    # Pre-populate form
    if request.method == 'GET':
        form.plan_id.data = user['plan_id']
        form.is_admin.data = bool(user['is_admin'])
    
    return render_template('admin_edit_user.html', form=form, user=user)

@bp.route('/user/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Delete a user and all their data"""
    if user_id == current_user.id:
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('admin.users'))
    
    user = query_db('SELECT * FROM user WHERE id = ?', (user_id,), one=True)
    if not user:
        flash('User not found.', 'error')
        return redirect(url_for('admin.users'))
    
    try:
        # Delete user's license keys
        execute_db('''
            DELETE FROM license_key 
            WHERE app_id IN (SELECT id FROM app WHERE owner_id = ?)
        ''', (user_id,))
        
        # Delete user's apps
        execute_db('DELETE FROM app WHERE owner_id = ?', (user_id,))
        
        # Delete user
        execute_db('DELETE FROM user WHERE id = ?', (user_id,))
        
        flash(f'User "{user["username"]}" and all associated data deleted.', 'success')
    except Exception as e:
        flash('Failed to delete user.', 'error')
    
    return redirect(url_for('admin.users'))

@bp.route('/apps')
@login_required
@admin_required
def apps():
    """Manage apps"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    search = request.args.get('search', '')
    
    # Build query
    base_query = '''
        SELECT a.*, u.username, COUNT(lk.id) as key_count
        FROM app a 
        JOIN user u ON a.owner_id = u.id
        LEFT JOIN license_key lk ON a.id = lk.app_id
    '''
    
    if search:
        base_query += ' WHERE a.name LIKE ? OR u.username LIKE ?'
        search_param = f'%{search}%'
        apps = query_db(base_query + ' GROUP BY a.id ORDER BY a.created_at DESC', 
                       (search_param, search_param))
    else:
        apps = query_db(base_query + ' GROUP BY a.id ORDER BY a.created_at DESC')
    
    # Simple pagination
    start = (page - 1) * per_page
    end = start + per_page
    paginated_apps = apps[start:end]
    
    has_prev = page > 1
    has_next = len(apps) > end
    
    return render_template('admin_apps.html', 
                         apps=paginated_apps,
                         page=page,
                         has_prev=has_prev,
                         has_next=has_next,
                         search=search)

@bp.route('/app/<int:app_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_app(app_id):
    """Delete an app and all its keys"""
    app = query_db('SELECT * FROM app WHERE id = ?', (app_id,), one=True)
    if not app:
        flash('App not found.', 'error')
        return redirect(url_for('admin.apps'))
    
    try:
        # Delete license keys
        execute_db('DELETE FROM license_key WHERE app_id = ?', (app_id,))
        # Delete app
        execute_db('DELETE FROM app WHERE id = ?', (app_id,))
        
        flash(f'App "{app["name"]}" and all its keys deleted.', 'success')
    except Exception as e:
        flash('Failed to delete app.', 'error')
    
    return redirect(url_for('admin.apps'))

@bp.route('/plans')
@login_required
@admin_required
def plans():
    """Manage plans"""
    plans = query_db('''
        SELECT p.*, COUNT(u.id) as user_count 
        FROM plan p 
        LEFT JOIN user u ON p.id = u.plan_id 
        GROUP BY p.id 
        ORDER BY p.id
    ''')
    
    return render_template('admin_plans.html', plans=plans)

@bp.route('/plan/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_plan():
    """Create a new plan"""
    form = AdminPlanForm()
    
    if form.validate_on_submit():
        try:
            execute_db(
                'INSERT INTO plan (name, max_apps, max_keys, rate_limit) VALUES (?, ?, ?, ?)',
                (form.name.data, form.max_apps.data, form.max_keys.data, form.rate_limit.data)
            )
            flash('Plan created successfully.', 'success')
            return redirect(url_for('admin.plans'))
        except Exception as e:
            flash('Failed to create plan. Plan name might already exist.', 'error')
    
    return render_template('admin_create_plan.html', form=form)

@bp.route('/plan/<int:plan_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_plan(plan_id):
    """Edit a plan"""
    plan = query_db('SELECT * FROM plan WHERE id = ?', (plan_id,), one=True)
    if not plan:
        flash('Plan not found.', 'error')
        return redirect(url_for('admin.plans'))
    
    form = AdminPlanForm(obj=plan)
    
    if form.validate_on_submit():
        try:
            execute_db(
                'UPDATE plan SET name = ?, max_apps = ?, max_keys = ?, rate_limit = ? WHERE id = ?',
                (form.name.data, form.max_apps.data, form.max_keys.data, 
                 form.rate_limit.data, plan_id)
            )
            flash('Plan updated successfully.', 'success')
            return redirect(url_for('admin.plans'))
        except Exception as e:
            flash('Failed to update plan.', 'error')
    
    return render_template('admin_edit_plan.html', form=form, plan=plan)

@bp.route('/plan/<int:plan_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_plan(plan_id):
    """Delete a plan (if no users are using it)"""
    if plan_id <= 3:  # Protect default plans
        flash('Cannot delete default plans.', 'error')
        return redirect(url_for('admin.plans'))
    
    # Check if any users are using this plan
    users_count = query_db(
        'SELECT COUNT(*) as count FROM user WHERE plan_id = ?',
        (plan_id,),
        one=True
    )['count']
    
    if users_count > 0:
        flash(f'Cannot delete plan. {users_count} users are currently using it.', 'error')
        return redirect(url_for('admin.plans'))
    
    try:
        execute_db('DELETE FROM plan WHERE id = ?', (plan_id,))
        flash('Plan deleted successfully.', 'success')
    except Exception as e:
        flash('Failed to delete plan.', 'error')
    
    return redirect(url_for('admin.plans'))

@bp.route('/api/stats')
@login_required
@admin_required
def api_stats():
    """API endpoint for admin statistics"""
    stats = {
        'users': query_db('SELECT COUNT(*) as count FROM user', one=True)['count'],
        'apps': query_db('SELECT COUNT(*) as count FROM app', one=True)['count'],
        'keys': query_db('SELECT COUNT(*) as count FROM license_key', one=True)['count'],
        'used_keys': query_db('SELECT COUNT(*) as count FROM license_key WHERE used = 1', one=True)['count']
    }
    
    return jsonify(stats)
