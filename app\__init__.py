import os
import sqlite3
import logging
from flask import Flask, g
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_bcrypt import Bcrypt
from flask_wtf.csrf import CSRFProtect
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# Initialize extensions
login_manager = LoginManager()
bcrypt = Bcrypt()
csrf = CSRFProtect()
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

def create_app(config=None):
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['DATABASE'] = os.path.join(app.instance_path, 'database.db')
    app.config['WTF_CSRF_TIME_LIMIT'] = None
    
    # Ensure instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass
    
    # Initialize extensions
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    bcrypt.init_app(app)
    csrf.init_app(app)
    limiter.init_app(app)
    
    # Setup logging
    if not app.debug:
        logging.basicConfig(
            filename='app.log',
            level=logging.INFO,
            format='%(asctime)s %(levelname)s %(name)s %(message)s'
        )
    
    # Database functions
    def close_db(e=None):
        """Close database connection"""
        db = g.pop('db', None)
        if db is not None:
            db.close()
    
    def init_db():
        """Initialize database with schema"""
        db = get_db()
        with app.open_resource('schema.sql') as f:
            db.executescript(f.read().decode('utf8'))
        db.commit()
    
    @app.cli.command()
    def init_db_command():
        """Clear existing data and create new tables"""
        init_db()
        print('Initialized the database.')
    
    # Register database functions
    app.teardown_appcontext(close_db)
    app.cli.add_command(init_db_command)
    
    # Make database functions available to templates
    app.jinja_env.globals.update(get_db=get_db)
    
    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from app.auth import User
        db = get_db()
        user_data = db.execute(
            'SELECT * FROM user WHERE id = ?', (user_id,)
        ).fetchone()
        if user_data:
            return User(user_data)
        return None
    
    # Register blueprints
    from app import auth, dashboard, admin, api
    app.register_blueprint(auth.bp)
    app.register_blueprint(dashboard.bp)
    app.register_blueprint(admin.bp)
    app.register_blueprint(api.bp)
    
    # Index route
    @app.route('/')
    def index():
        from flask import render_template
        return render_template('index.html')
    
    # Initialize database on first run
    with app.app_context():
        init_db()
    
    return app

# Database helper functions
def get_db():
    """Get database connection"""
    if 'db' not in g:
        from flask import current_app
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

def query_db(query, args=(), one=False):
    """Execute a query and return results"""
    db = get_db()
    cur = db.execute(query, args)
    rv = cur.fetchall()
    cur.close()
    return (rv[0] if rv else None) if one else rv

def execute_db(query, args=()):
    """Execute a query and commit changes"""
    db = get_db()
    cur = db.execute(query, args)
    db.commit()
    return cur.lastrowid
