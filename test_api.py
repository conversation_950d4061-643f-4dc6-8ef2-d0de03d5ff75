#!/usr/bin/env python3
"""
Test API functionality
"""

from app import create_app
import json

def test_api():
    """Test API endpoints"""
    print("🔍 Testing API endpoints...")
    
    app = create_app()
    
    with app.test_client() as client:
        try:
            # Test API with missing parameters
            response = client.post('/api/v1/auth', json={})
            print(f"API auth (empty) status: {response.status_code}")

            if response.status_code == 400:
                data = response.get_json()
                if data and 'message' in data:
                    print(f"✅ API correctly returns 400 for missing parameters: {data['message']}")
                else:
                    print("✅ API correctly returns 400 for missing parameters")
            
            # Test API with non-existent app
            response = client.post('/api/v1/auth', json={
                'app_name': 'NonExistentApp',
                'key': 'invalid-key'
            })
            print(f"API auth (invalid app) status: {response.status_code}")
            
            if response.status_code == 404:
                data = response.get_json()
                if data and 'message' in data:
                    print(f"✅ API correctly returns 404 for non-existent app: {data['message']}")
                else:
                    print("✅ API correctly returns 404 for non-existent app")
            
            # Test API with demo app but invalid key
            response = client.post('/api/v1/auth', json={
                'app_name': 'DemoApp',
                'key': 'invalid-key'
            })
            print(f"API auth (invalid key) status: {response.status_code}")
            
            if response.status_code == 403:
                data = response.get_json()
                if data and 'message' in data:
                    print(f"✅ API correctly returns 403 for invalid key: {data['message']}")
                else:
                    print("✅ API correctly returns 403 for invalid key")
            
            # Test check_key endpoint
            response = client.post('/api/v1/check_key', json={
                'app_name': 'DemoApp',
                'key': 'invalid-key'
            })
            print(f"API check_key status: {response.status_code}")
            
            if response.status_code == 404:
                data = response.get_json()
                if data and 'message' in data:
                    print(f"✅ API check_key correctly returns 404 for invalid key: {data['message']}")
                else:
                    print("✅ API check_key correctly returns 404 for invalid key")
            
            # Test app_info endpoint
            response = client.post('/api/v1/app_info', json={
                'app_name': 'DemoApp'
            })
            print(f"API app_info status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ API app_info works: {data['app_info']['name']}")
            
            print("✅ All API endpoints working correctly!")
            
        except Exception as e:
            print(f"❌ API test error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_api()
