#!/usr/bin/env python3
"""
Test the database fix
"""

from app import create_app, query_db

def test_database_functions():
    """Test that database functions work correctly"""
    print("🔍 Testing database functions...")
    
    app = create_app()
    with app.app_context():
        try:
            # Test query_db function
            plans = query_db('SELECT * FROM plan')
            print(f"✅ query_db works - found {len(plans)} plans")
            
            # Test specific query
            free_plan = query_db('SELECT * FROM plan WHERE name = ?', ('Free',), one=True)
            if free_plan:
                print(f"✅ Free plan found: {free_plan['name']} - {free_plan['max_apps']} apps, {free_plan['max_keys']} keys")
            else:
                print("❌ Free plan not found")
                
            # Test user query (should work now)
            users = query_db('SELECT * FROM user')
            print(f"✅ User query works - found {len(users)} users")
            
            print("✅ All database functions working correctly!")
            
        except Exception as e:
            print(f"❌ Database function error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_database_functions()
